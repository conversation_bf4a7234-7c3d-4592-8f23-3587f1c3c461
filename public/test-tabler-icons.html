<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tabler Icons Font Test</title>
    <link href="assets/css/icons.css" rel="stylesheet">
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .icon-test {
            font-size: 24px;
            margin: 10px;
            display: inline-block;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
    </style>
</head>
<body>
    <h1>Tabler Icons Font Loading Test</h1>
    
    <div class="test-container">
        <h2>Font Loading Status</h2>
        <div id="font-status" class="status">Checking font loading...</div>
    </div>
    
    <div class="test-container">
        <h2>Sample Tabler Icons</h2>
        <p>If the fonts are loading correctly, you should see icons below instead of squares or missing characters:</p>
        
        <div class="icon-test">
            <i class="ti ti-home"></i> Home
        </div>
        <div class="icon-test">
            <i class="ti ti-user"></i> User
        </div>
        <div class="icon-test">
            <i class="ti ti-settings"></i> Settings
        </div>
        <div class="icon-test">
            <i class="ti ti-heart"></i> Heart
        </div>
        <div class="icon-test">
            <i class="ti ti-star"></i> Star
        </div>
        <div class="icon-test">
            <i class="ti ti-check"></i> Check
        </div>
    </div>
    
    <div class="test-container">
        <h2>Font File URLs</h2>
        <p>The following font files should be accessible:</p>
        <ul id="font-urls"></ul>
    </div>

    <script>
        // Test font loading with improved error handling
        document.fonts.ready.then(function() {
            const fontStatus = document.getElementById('font-status');

            try {
                const tablerFont = document.fonts.check('1em tabler-icons');

                if (tablerFont) {
                    fontStatus.textContent = '✅ Tabler Icons font loaded successfully!';
                    fontStatus.className = 'status success';
                    console.log('✅ Font check passed: tabler-icons is available');
                } else {
                    fontStatus.textContent = '❌ Tabler Icons font failed to load!';
                    fontStatus.className = 'status error';
                    console.log('❌ Font check failed: tabler-icons is not available');
                }
            } catch (error) {
                fontStatus.textContent = '⚠️ Error checking font status!';
                fontStatus.className = 'status error';
                console.error('Error checking font:', error);
            }

            // Log all loaded fonts with error handling
            try {
                const allFonts = Array.from(document.fonts).map(f => ({
                    family: f.family,
                    status: f.status,
                    style: f.style,
                    weight: f.weight
                }));
                console.log('All loaded fonts:', allFonts);

                // Check if tabler-icons is in the list
                const tablerFontObj = Array.from(document.fonts).find(f => f.family === 'tabler-icons');
                if (tablerFontObj) {
                    console.log('Tabler font object found:', {
                        family: tablerFontObj.family,
                        status: tablerFontObj.status,
                        style: tablerFontObj.style,
                        weight: tablerFontObj.weight
                    });
                } else {
                    console.log('Tabler font not found in document.fonts collection');
                }
            } catch (error) {
                console.error('Error analyzing fonts:', error);
            }
        }).catch(function(error) {
            console.error('Error waiting for fonts to be ready:', error);
            const fontStatus = document.getElementById('font-status');
            fontStatus.textContent = '⚠️ Font loading timeout or error!';
            fontStatus.className = 'status error';
        });

        // Monitor font loading events
        document.fonts.addEventListener('loading', function(event) {
            // For FontFaceSetLoadEvent, the fontfaces property contains an array of FontFace objects
            if (event.fontfaces && event.fontfaces.length > 0) {
                event.fontfaces.forEach(fontface => {
                    console.log('Font loading started:', fontface.family);
                });
            } else {
                console.log('Font loading started (no specific font info available)');
            }
        });

        document.fonts.addEventListener('loadingdone', function(event) {
            // For FontFaceSetLoadEvent, the fontfaces property contains an array of FontFace objects
            if (event.fontfaces && event.fontfaces.length > 0) {
                event.fontfaces.forEach(fontface => {
                    console.log('Font loading done:', fontface.family);
                });
            } else {
                console.log('Font loading done (no specific font info available)');
            }
        });

        document.fonts.addEventListener('loadingerror', function(event) {
            // For FontFaceSetLoadEvent, the fontfaces property contains an array of FontFace objects
            if (event.fontfaces && event.fontfaces.length > 0) {
                event.fontfaces.forEach(fontface => {
                    console.error('Font loading error:', fontface.family, fontface);
                });
            } else {
                console.error('Font loading error (no specific font info available)', event);
            }
        });

        // List font URLs for debugging
        const fontUrls = [
            'assets/icon-fonts/tabler-icons/iconfont/fonts/tabler-icons.woff2',
            'assets/icon-fonts/tabler-icons/iconfont/fonts/tabler-icons.woff',
            'assets/icon-fonts/tabler-icons/iconfont/fonts/tabler-icons.ttf',
            'assets/icon-fonts/tabler-icons/iconfont/fonts/tabler-icons.eot',
            'assets/icon-fonts/tabler-icons/iconfont/fonts/tabler-icons.svg'
        ];

        const urlList = document.getElementById('font-urls');
        fontUrls.forEach(url => {
            const li = document.createElement('li');
            const link = document.createElement('a');
            link.href = url;
            link.textContent = url;
            link.target = '_blank';
            li.appendChild(link);
            urlList.appendChild(li);
        });

        // Test font URLs accessibility
        fontUrls.forEach(url => {
            fetch(url, { method: 'HEAD' })
                .then(response => {
                    console.log(`Font URL ${url}: ${response.status} ${response.statusText}`);
                })
                .catch(error => {
                    console.error(`Font URL ${url}: Error -`, error);
                });
        });
    </script>
</body>
</html>
