<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tabler Icons Direct Test</title>
    <!-- Direct link to tabler-icons.css instead of through icons.css -->
    <link href="assets/icon-fonts/tabler-icons/iconfont/tabler-icons.css" rel="stylesheet">
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .icon-test {
            font-size: 24px;
            margin: 10px;
            display: inline-block;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
    </style>
</head>
<body>
    <h1>Tabler Icons Direct Loading Test</h1>
    
    <div class="test-container">
        <h2>Font Loading Status</h2>
        <div id="font-status" class="status">Checking font loading...</div>
    </div>
    
    <div class="test-container">
        <h2>Sample Tabler Icons</h2>
        <p>Testing direct CSS link (not through icons.css import):</p>
        
        <div class="icon-test">
            <i class="ti ti-home"></i> Home
        </div>
        <div class="icon-test">
            <i class="ti ti-user"></i> User
        </div>
        <div class="icon-test">
            <i class="ti ti-settings"></i> Settings
        </div>
        <div class="icon-test">
            <i class="ti ti-heart"></i> Heart
        </div>
        <div class="icon-test">
            <i class="ti ti-star"></i> Star
        </div>
        <div class="icon-test">
            <i class="ti ti-check"></i> Check
        </div>
    </div>

    <script>
        // Test font loading with improved error handling
        document.fonts.ready.then(function() {
            const fontStatus = document.getElementById('font-status');

            try {
                const tablerFont = document.fonts.check('1em tabler-icons');

                if (tablerFont) {
                    fontStatus.textContent = '✅ Tabler Icons font loaded successfully (direct link)!';
                    fontStatus.className = 'status success';
                    console.log('✅ Direct CSS: Font check passed for tabler-icons');
                } else {
                    fontStatus.textContent = '❌ Tabler Icons font failed to load (direct link)!';
                    fontStatus.className = 'status error';
                    console.log('❌ Direct CSS: Font check failed for tabler-icons');
                }
            } catch (error) {
                fontStatus.textContent = '⚠️ Error checking font status (direct link)!';
                fontStatus.className = 'status error';
                console.error('Direct CSS: Error checking font:', error);
            }

            // Log all loaded fonts with error handling
            try {
                const allFonts = Array.from(document.fonts).map(f => ({
                    family: f.family,
                    status: f.status
                }));
                console.log('Direct CSS: All loaded fonts:', allFonts);
            } catch (error) {
                console.error('Direct CSS: Error analyzing fonts:', error);
            }
        }).catch(function(error) {
            console.error('Direct CSS: Error waiting for fonts to be ready:', error);
            const fontStatus = document.getElementById('font-status');
            fontStatus.textContent = '⚠️ Font loading timeout or error (direct link)!';
            fontStatus.className = 'status error';
        });
        
        // Monitor font loading events
        document.fonts.addEventListener('loadingerror', function(event) {
            // For FontFaceSetLoadEvent, the fontfaces property contains an array of FontFace objects
            if (event.fontfaces && event.fontfaces.length > 0) {
                event.fontfaces.forEach(fontface => {
                    console.error('Font loading error:', fontface.family, fontface);
                });
            } else {
                console.error('Font loading error (no specific font info available)', event);
            }
        });

        // Additional font loading monitoring
        document.fonts.addEventListener('loading', function(event) {
            if (event.fontfaces && event.fontfaces.length > 0) {
                event.fontfaces.forEach(fontface => {
                    console.log('Font loading started:', fontface.family);
                });
            }
        });

        document.fonts.addEventListener('loadingdone', function(event) {
            if (event.fontfaces && event.fontfaces.length > 0) {
                event.fontfaces.forEach(fontface => {
                    console.log('Font loading completed:', fontface.family);
                });
            }
        });
    </script>
</body>
</html>
